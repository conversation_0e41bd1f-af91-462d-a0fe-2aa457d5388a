import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosResponse } from 'axios';

export interface BlockchainTransferRequest {
  tokenId: string;
  contractAddress: string;
  fromAddress: string;
  toAddress: string;
  transactionId: string;
}

export interface BlockchainTransferResponse {
  success: boolean;
  transactionHash: string;
  blockNumber?: number;
  gasUsed?: string;
  status: 'pending' | 'confirmed' | 'failed';
  error?: string;
}

export interface NFTOwnershipInfo {
  tokenId: string;
  contractAddress: string;
  owner: string;
  isApproved: boolean;
  metadata?: any;
}

@Injectable()
export class BlockchainService {
  private readonly logger = new Logger(BlockchainService.name);
  private readonly blockchainServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.blockchainServiceUrl = this.configService.get<string>('BLOCKCHAIN_SERVICE_URL', 'http://localhost:3021');
  }

  async transferNFTOwnership(transferRequest: BlockchainTransferRequest): Promise<BlockchainTransferResponse> {
    try {
      this.logger.log(`Initiating NFT ownership transfer for token ${transferRequest.tokenId}`);

      const response: AxiosResponse<any> = await firstValueFrom(
        this.httpService.post(`${this.blockchainServiceUrl}/api/nft/transfer`, {
          tokenId: transferRequest.tokenId,
          contractAddress: transferRequest.contractAddress,
          from: transferRequest.fromAddress,
          to: transferRequest.toAddress,
          transactionId: transferRequest.transactionId,
        })
      );

      this.logger.log(`NFT transfer response: ${JSON.stringify(response.data)}`);

      return {
        success: response.data.success,
        transactionHash: response.data.transactionHash,
        blockNumber: response.data.blockNumber,
        gasUsed: response.data.gasUsed,
        status: response.data.status,
        error: response.data.error,
      };
    } catch (error) {
      this.logger.error(`Failed to transfer NFT ownership: ${error.message}`, error.stack);

      return {
        success: false,
        transactionHash: '',
        status: 'failed',
        error: error.response?.data?.message || error.message,
      };
    }
  }

  async verifyNFTOwnership(tokenId: string, contractAddress: string): Promise<NFTOwnershipInfo> {
    try {
      this.logger.log(`Verifying NFT ownership for token ${tokenId}`);

      const response: AxiosResponse<any> = await firstValueFrom(
        this.httpService.get(`${this.blockchainServiceUrl}/api/nft/ownership`, {
          params: {
            tokenId,
            contractAddress,
          },
        })
      );

      return {
        tokenId: response.data.tokenId,
        contractAddress: response.data.contractAddress,
        owner: response.data.owner,
        isApproved: response.data.isApproved,
        metadata: response.data.metadata,
      };
    } catch (error) {
      this.logger.error(`Failed to verify NFT ownership: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to verify NFT ownership: ${error.message}`);
    }
  }

  async approveMarketplace(tokenId: string, contractAddress: string, ownerAddress: string): Promise<BlockchainTransferResponse> {
    try {
      this.logger.log(`Approving marketplace for token ${tokenId}`);

      const response: AxiosResponse<any> = await firstValueFrom(
        this.httpService.post(`${this.blockchainServiceUrl}/api/nft/approve`, {
          tokenId,
          contractAddress,
          owner: ownerAddress,
          spender: 'marketplace_contract_address', // This would be the actual marketplace contract
        })
      );

      return {
        success: response.data.success,
        transactionHash: response.data.transactionHash,
        blockNumber: response.data.blockNumber,
        status: response.data.status,
        error: response.data.error,
      };
    } catch (error) {
      this.logger.error(`Failed to approve marketplace: ${error.message}`, error.stack);

      return {
        success: false,
        transactionHash: '',
        status: 'failed',
        error: error.response?.data?.message || error.message,
      };
    }
  }

  async getTransactionStatus(transactionHash: string): Promise<{
    status: 'pending' | 'confirmed' | 'failed';
    blockNumber?: number;
    gasUsed?: string;
    confirmations?: number;
  }> {
    try {
      this.logger.log(`Getting transaction status for ${transactionHash}`);

      const response: AxiosResponse<any> = await firstValueFrom(
        this.httpService.get(`${this.blockchainServiceUrl}/api/transaction/status`, {
          params: { transactionHash },
        })
      );

      return {
        status: response.data.status,
        blockNumber: response.data.blockNumber,
        gasUsed: response.data.gasUsed,
        confirmations: response.data.confirmations,
      };
    } catch (error) {
      this.logger.error(`Failed to get transaction status: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get transaction status: ${error.message}`);
    }
  }

  async estimateGasFee(transferRequest: Omit<BlockchainTransferRequest, 'transactionId'>): Promise<{
    gasEstimate: string;
    gasPriceGwei: string;
    estimatedCostEth: string;
  }> {
    try {
      this.logger.log(`Estimating gas fee for NFT transfer`);

      const response: AxiosResponse<any> = await firstValueFrom(
        this.httpService.post(`${this.blockchainServiceUrl}/api/gas/estimate`, {
          tokenId: transferRequest.tokenId,
          contractAddress: transferRequest.contractAddress,
          from: transferRequest.fromAddress,
          to: transferRequest.toAddress,
        })
      );

      return {
        gasEstimate: response.data.gasEstimate,
        gasPriceGwei: response.data.gasPriceGwei,
        estimatedCostEth: response.data.estimatedCostEth,
      };
    } catch (error) {
      this.logger.error(`Failed to estimate gas fee: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to estimate gas fee: ${error.message}`);
    }
  }

  async validateContractAddress(contractAddress: string): Promise<boolean> {
    try {
      const response: AxiosResponse<any> = await firstValueFrom(
        this.httpService.get(`${this.blockchainServiceUrl}/api/contract/validate`, {
          params: { contractAddress },
        })
      );

      return response.data.isValid;
    } catch (error) {
      this.logger.error(`Failed to validate contract address: ${error.message}`, error.stack);
      return false;
    }
  }
}
