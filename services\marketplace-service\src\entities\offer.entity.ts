import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm';
import { NFTListing } from './nft-listing.entity';

export enum OfferStatus {
  ACTIVE = 'active',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
}

@Entity('offers')
@Index(['bidderId'])
@Index(['listingId'])
@Index(['status'])
@Index(['createdAt'])
@Index(['expiresAt'])
export class Offer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  listingId: string; // Reference to NFT listing

  @Column({ nullable: false })
  nftId: string; // Reference to NFT

  @Column({ nullable: false })
  bidderId: string; // User ID of the bidder

  @Column({ nullable: false })
  sellerId: string; // User ID of the seller (for notifications)

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: false })
  amount: string; // Offer amount

  @Column({ nullable: false, default: 'ETH' })
  currency: string; // Currency type

  @Column({
    type: 'enum',
    enum: OfferStatus,
    default: OfferStatus.ACTIVE,
  })
  status: OfferStatus;

  @Column({ nullable: true })
  message: string; // Optional message from bidder

  @Column({ type: 'timestamp', nullable: false })
  expiresAt: Date; // When offer expires

  @Column({ type: 'timestamp', nullable: true })
  acceptedAt: Date; // When offer was accepted

  @Column({ type: 'timestamp', nullable: true })
  rejectedAt: Date; // When offer was rejected

  @Column({ nullable: true })
  rejectionReason: string; // Reason for rejection

  @Column({ type: 'json', nullable: true })
  metadata: any; // Additional offer metadata

  @ManyToOne(() => NFTListing, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'listingId' })
  listing: NFTListing;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
