import { Controller, Get } from '@nestjs/common';
import { ProxyService } from '../services/proxy.service';

@Controller('environment')
export class EnvironmentController {
  constructor(private readonly proxyService: ProxyService) {}

  /**
   * Get current environment information
   * GET /api/environment/info
   */
  @Get('info')
  getEnvironmentInfo() {
    return {
      status: 'success',
      data: this.proxyService.getEnvironmentInfo(),
      message: 'Environment information retrieved successfully'
    };
  }

  /**
   * Get all service statuses
   * GET /api/environment/services
   */
  @Get('services')
  async getServiceStatuses() {
    try {
      const statuses = await this.proxyService.getAllServiceStatuses();
      return {
        status: 'success',
        data: statuses,
        message: 'Service statuses retrieved successfully'
      };
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: `Failed to retrieve service statuses: ${error.message}`
      };
    }
  }

  /**
   * Health check for specific service
   * GET /api/environment/health/:serviceName
   */
  @Get('health/:serviceName')
  async checkServiceHealth(serviceName: string) {
    try {
      const isHealthy = await this.proxyService.healthCheck(serviceName);
      return {
        status: 'success',
        data: {
          serviceName,
          healthy: isHealthy,
          timestamp: new Date().toISOString()
        },
        message: `Health check for ${serviceName} completed`
      };
    } catch (error) {
      return {
        status: 'error',
        data: {
          serviceName,
          healthy: false,
          timestamp: new Date().toISOString()
        },
        message: `Health check failed for ${serviceName}: ${error.message}`
      };
    }
  }
}
