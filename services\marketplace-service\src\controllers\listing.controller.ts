import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Headers,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { ListingService } from '../services/listing.service';
import { CreateListingDto } from '../dto/create-listing.dto';
import { UpdateListingDto } from '../dto/update-listing.dto';
import { NFTListing, ListingStatus } from '../entities/nft-listing.entity';

@ApiTags('Marketplace - Listings')
@Controller('marketplace/listings')
export class ListingController {
  constructor(private readonly listingService: ListingService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new NFT listing' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Listing created successfully', type: NFTListing })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data' })
  @ApiBearerAuth()
  async createListing(
    @Body() createListingDto: CreateListingDto,
    @Headers('x-user-id') userId: string,
  ): Promise<NFTListing> {
    return await this.listingService.createListing(createListingDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all NFT listings with filters' })
  @ApiQuery({ name: 'status', required: false, enum: ListingStatus })
  @ApiQuery({ name: 'sellerId', required: false, description: 'Filter by seller ID' })
  @ApiQuery({ name: 'minPrice', required: false, description: 'Minimum price filter' })
  @ApiQuery({ name: 'maxPrice', required: false, description: 'Maximum price filter' })
  @ApiQuery({ name: 'currency', required: false, description: 'Currency filter' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results per page' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of results to skip' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Listings retrieved successfully' })
  async findAllListings(
    @Query('status') status?: ListingStatus,
    @Query('sellerId') sellerId?: string,
    @Query('minPrice') minPrice?: string,
    @Query('maxPrice') maxPrice?: string,
    @Query('currency') currency?: string,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string,
  ) {
    const filters = {
      status,
      sellerId,
      minPrice,
      maxPrice,
      currency,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
    };

    return await this.listingService.findAllListings(filters);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get listing by ID' })
  @ApiParam({ name: 'id', description: 'Listing ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Listing retrieved successfully', type: NFTListing })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Listing not found' })
  async findListingById(@Param('id') id: string): Promise<NFTListing> {
    return await this.listingService.findListingById(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update listing' })
  @ApiParam({ name: 'id', description: 'Listing ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Listing updated successfully', type: NFTListing })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Listing not found' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Not authorized to update this listing' })
  @ApiBearerAuth()
  async updateListing(
    @Param('id') id: string,
    @Body() updateListingDto: UpdateListingDto,
    @Headers('x-user-id') userId: string,
  ): Promise<NFTListing> {
    return await this.listingService.updateListing(id, updateListingDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Cancel listing' })
  @ApiParam({ name: 'id', description: 'Listing ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Listing cancelled successfully', type: NFTListing })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Listing not found' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Not authorized to cancel this listing' })
  @ApiBearerAuth()
  async cancelListing(
    @Param('id') id: string,
    @Headers('x-user-id') userId: string,
  ): Promise<NFTListing> {
    return await this.listingService.cancelListing(id, userId);
  }

  @Get('seller/:sellerId')
  @ApiOperation({ summary: 'Get listings by seller ID' })
  @ApiParam({ name: 'sellerId', description: 'Seller user ID' })
  @ApiQuery({ name: 'status', required: false, enum: ListingStatus })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results per page' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of results to skip' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Seller listings retrieved successfully' })
  async getSellerListings(
    @Param('sellerId') sellerId: string,
    @Query('status') status?: ListingStatus,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string,
  ) {
    const filters = {
      sellerId,
      status,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
    };

    return await this.listingService.findAllListings(filters);
  }
}
