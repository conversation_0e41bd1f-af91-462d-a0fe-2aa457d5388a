import { IsString, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class PurchaseNFTDto {
  @ApiProperty({ description: 'Listing ID to purchase' })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  listingId: string;

  @ApiProperty({ description: 'Expected price (for validation)', required: false })
  @IsString()
  @IsOptional()
  expectedPrice?: string;

  @ApiProperty({ description: 'Buyer wallet address' })
  @IsString()
  @IsNotEmpty()
  buyerWalletAddress: string;

  @ApiProperty({ description: 'Payment transaction hash', required: false })
  @IsString()
  @IsOptional()
  paymentTxHash?: string;
}
