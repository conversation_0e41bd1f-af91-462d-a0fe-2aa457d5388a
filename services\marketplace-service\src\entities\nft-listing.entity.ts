import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export enum ListingStatus {
  ACTIVE = 'active',
  SOLD = 'sold',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
}

export enum ListingType {
  FIXED_PRICE = 'fixed_price',
  AUCTION = 'auction',
  BUNDLE = 'bundle',
}

@Entity('nft_listings')
@Index(['sellerId'])
@Index(['status'])
@Index(['listingType'])
@Index(['createdAt'])
export class NFTListing {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  nftId: string; // Reference to NFT from NFT Generation Service

  @Column({ nullable: false })
  sellerId: string; // User ID of the seller

  @Column({ nullable: false })
  tokenId: string; // Blockchain token ID

  @Column({ nullable: false })
  contractAddress: string; // Smart contract address

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: false })
  price: string; // Price in ETH/MATIC

  @Column({ nullable: false, default: 'ETH' })
  currency: string; // Currency type (ETH, MATIC, etc.)

  @Column({
    type: 'enum',
    enum: ListingStatus,
    default: ListingStatus.ACTIVE,
  })
  status: ListingStatus;

  @Column({
    type: 'enum',
    enum: ListingType,
    default: ListingType.FIXED_PRICE,
  })
  listingType: ListingType;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: '2.5' })
  platformFeePercentage: string; // Platform fee percentage

  @Column({ type: 'decimal', precision: 5, scale: 2, default: '5.0' })
  royaltyPercentage: string; // Creator royalty percentage

  @Column({ nullable: true })
  originalCreatorId: string; // Original NFT creator for royalties

  @Column({ type: 'json', nullable: true })
  metadata: any; // Additional listing metadata

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
