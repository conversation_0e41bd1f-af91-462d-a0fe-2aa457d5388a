import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  Headers,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { TransactionService } from '../services/transaction.service';
import { PurchaseNFTDto } from '../dto/purchase-nft.dto';
import { Transaction, TransactionStatus } from '../entities/transaction.entity';

@ApiTags('Marketplace - Transactions')
@Controller('marketplace/transactions')
export class TransactionController {
  constructor(private readonly transactionService: TransactionService) {}

  @Post('purchase')
  @ApiOperation({ summary: 'Purchase an NFT from a listing' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Purchase transaction created successfully', type: Transaction })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid purchase request' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Listing not found' })
  @ApiBearerAuth()
  async purchaseNFT(
    @Body() purchaseDto: PurchaseNFTDto,
    @Headers('x-user-id') userId: string,
  ): Promise<Transaction> {
    return await this.transactionService.purchaseNFT(purchaseDto, userId);
  }

  @Patch(':id/confirm')
  @ApiOperation({ summary: 'Confirm a transaction with blockchain details' })
  @ApiParam({ name: 'id', description: 'Transaction ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Transaction confirmed successfully', type: Transaction })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Transaction not found' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Transaction cannot be confirmed' })
  async confirmTransaction(
    @Param('id') id: string,
    @Body() confirmData: { blockchainTxHash: string; blockNumber?: number },
  ): Promise<Transaction> {
    return await this.transactionService.confirmTransaction(
      id,
      confirmData.blockchainTxHash,
      confirmData.blockNumber,
    );
  }

  @Patch(':id/fail')
  @ApiOperation({ summary: 'Mark a transaction as failed' })
  @ApiParam({ name: 'id', description: 'Transaction ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Transaction marked as failed', type: Transaction })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Transaction not found' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Transaction cannot be failed' })
  async failTransaction(
    @Param('id') id: string,
    @Body() failData: { reason?: string },
  ): Promise<Transaction> {
    return await this.transactionService.failTransaction(id, failData.reason);
  }

  @Get('history')
  @ApiOperation({ summary: 'Get transaction history for user' })
  @ApiQuery({ name: 'type', required: false, enum: ['buyer', 'seller'], description: 'Filter by user role' })
  @ApiQuery({ name: 'status', required: false, enum: TransactionStatus })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results per page' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of results to skip' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Transaction history retrieved successfully' })
  @ApiBearerAuth()
  async getTransactionHistory(
    @Headers('x-user-id') userId: string,
    @Query('type') type?: 'buyer' | 'seller',
    @Query('status') status?: TransactionStatus,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string,
  ) {
    const filters = {
      type,
      status,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
    };

    return await this.transactionService.getTransactionHistory(userId, filters);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get transaction by ID' })
  @ApiParam({ name: 'id', description: 'Transaction ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Transaction retrieved successfully', type: Transaction })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Transaction not found' })
  async getTransactionById(@Param('id') id: string): Promise<Transaction> {
    // This would need to be implemented in the service
    // For now, we'll return a placeholder
    throw new Error('Method not implemented');
  }
}
