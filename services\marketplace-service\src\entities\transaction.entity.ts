import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm';
import { NFTListing } from './nft-listing.entity';

export enum TransactionStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum TransactionType {
  PURCHASE = 'purchase',
  OFFER_ACCEPTED = 'offer_accepted',
  AUCTION_WIN = 'auction_win',
  TRANSFER = 'transfer',
}

@Entity('transactions')
@Index(['buyerId'])
@Index(['sellerId'])
@Index(['status'])
@Index(['transactionType'])
@Index(['createdAt'])
export class Transaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  listingId: string; // Reference to NFT listing

  @Column({ nullable: false })
  nftId: string; // Reference to NFT

  @Column({ nullable: false })
  buyerId: string; // User ID of the buyer

  @Column({ nullable: false })
  sellerId: string; // User ID of the seller

  @Column({ nullable: false })
  tokenId: string; // Blockchain token ID

  @Column({ nullable: false })
  contractAddress: string; // Smart contract address

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: false })
  amount: string; // Transaction amount

  @Column({ nullable: false, default: 'ETH' })
  currency: string; // Currency type

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: false })
  platformFee: string; // Platform fee amount

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: false })
  royaltyFee: string; // Creator royalty amount

  @Column({ type: 'decimal', precision: 18, scale: 8, nullable: false })
  sellerAmount: string; // Amount received by seller

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  status: TransactionStatus;

  @Column({
    type: 'enum',
    enum: TransactionType,
    default: TransactionType.PURCHASE,
  })
  transactionType: TransactionType;

  @Column({ nullable: true })
  blockchainTxHash: string; // Blockchain transaction hash

  @Column({ nullable: true })
  blockNumber: number; // Block number where transaction was mined

  @Column({ type: 'timestamp', nullable: true })
  confirmedAt: Date; // When transaction was confirmed on blockchain

  @Column({ type: 'json', nullable: true })
  metadata: any; // Additional transaction metadata

  @ManyToOne(() => NFTListing, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'listingId' })
  listing: NFTListing;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
