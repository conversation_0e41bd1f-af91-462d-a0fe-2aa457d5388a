import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('marketplace_config')
export class MarketplaceConfig {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, nullable: false })
  key: string; // Configuration key

  @Column({ nullable: false })
  value: string; // Configuration value

  @Column({ nullable: true })
  description: string; // Description of the configuration

  @Column({ default: true })
  isActive: boolean; // Whether this config is active

  @Column({ nullable: true })
  category: string; // Configuration category (fees, limits, etc.)

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

// Default marketplace configurations
export const DEFAULT_MARKETPLACE_CONFIG = {
  PLATFORM_FEE_PERCENTAGE: '2.5',
  DEFAULT_ROYALTY_PERCENTAGE: '5.0',
  MAX_LISTING_DURATION_DAYS: '30',
  MIN_LISTING_PRICE: '0.001',
  MAX_LISTING_PRICE: '1000000',
  DEFAULT_OFFER_DURATION_HOURS: '24',
  MAX_OFFERS_PER_LISTING: '10',
  SUPPORTED_CURRENCIES: 'ETH,MATIC,USDC',
  ESCROW_ENABLED: 'true',
  AUTO_ACCEPT_OFFERS: 'false',
};
