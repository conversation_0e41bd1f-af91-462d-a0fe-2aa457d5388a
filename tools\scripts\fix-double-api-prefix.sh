#!/bin/bash

# =============================================================================
# DOUBLE API PREFIX FIX SCRIPT
# =============================================================================
# This script fixes the systemic double API prefix issue across all services
# 
# ISSUE: Services have both:
# - Global prefix: app.setGlobalPrefix('api') 
# - Controller paths: @Controller('api/...')
# - Result: /api/api/... (WRONG)
#
# SOLUTION: Keep global prefix, remove 'api/' from controller paths
# - Global prefix: app.setGlobalPrefix('api')
# - Controller paths: @Controller('marketplace/listings')  
# - Result: /api/marketplace/listings (CORRECT)
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "services" ]; then
    log_error "This script must be run from the project root directory"
    exit 1
fi

log_info "🔧 Starting Double API Prefix Fix..."
echo "=================================="

# Services to fix (excluding api-gateway which has different structure)
SERVICES=(
    "project-service"
    "nft-generation-service" 
    "notification-service"
    "analytics-service"
)

# Function to fix controller paths in a service
fix_service_controllers() {
    local service_name=$1
    local service_path="services/$service_name"
    
    if [ ! -d "$service_path" ]; then
        log_warning "Service directory not found: $service_path"
        return 1
    fi
    
    log_info "Fixing controllers in $service_name..."
    
    # Find all controller files
    local controller_files=$(find "$service_path/src" -name "*.controller.ts" 2>/dev/null || echo "")
    
    if [ -z "$controller_files" ]; then
        log_warning "No controller files found in $service_name"
        return 0
    fi
    
    local fixed_count=0
    
    # Process each controller file
    while IFS= read -r file; do
        if [ -f "$file" ]; then
            # Check if file contains @Controller('api/...)
            if grep -q "@Controller('api/" "$file"; then
                log_info "  Fixing: $(basename "$file")"
                
                # Create backup
                cp "$file" "$file.backup"
                
                # Fix the controller path by removing 'api/' prefix
                sed -i "s/@Controller('api\//@Controller('/g" "$file"
                
                # Verify the change was made
                if ! grep -q "@Controller('api/" "$file"; then
                    ((fixed_count++))
                    log_success "    ✅ Fixed controller path in $(basename "$file")"
                else
                    log_error "    ❌ Failed to fix $(basename "$file")"
                    # Restore backup
                    mv "$file.backup" "$file"
                fi
            else
                log_info "  ✅ $(basename "$file") already correct"
            fi
        fi
    done <<< "$controller_files"
    
    if [ $fixed_count -gt 0 ]; then
        log_success "Fixed $fixed_count controller(s) in $service_name"
    else
        log_info "No fixes needed in $service_name"
    fi
    
    return 0
}

# Function to verify service has global prefix
verify_global_prefix() {
    local service_name=$1
    local service_path="services/$service_name"
    local main_file="$service_path/src/main.ts"
    
    if [ ! -f "$main_file" ]; then
        log_warning "main.ts not found in $service_name"
        return 1
    fi
    
    if grep -q "setGlobalPrefix.*api" "$main_file"; then
        log_success "  ✅ Global prefix 'api' confirmed in $service_name"
        return 0
    else
        log_warning "  ⚠️  Global prefix 'api' not found in $service_name"
        return 1
    fi
}

# Main execution
echo ""
log_info "🔍 PHASE 1: VERIFYING GLOBAL PREFIXES"
echo "====================================="

for service in "${SERVICES[@]}"; do
    verify_global_prefix "$service"
done

echo ""
log_info "🔧 PHASE 2: FIXING CONTROLLER PATHS"
echo "==================================="

total_services_fixed=0

for service in "${SERVICES[@]}"; do
    echo ""
    if fix_service_controllers "$service"; then
        ((total_services_fixed++))
    fi
done

echo ""
log_info "🧪 PHASE 3: VERIFICATION"
echo "======================="

# Verify no double prefixes remain
log_info "Checking for remaining double API prefixes..."

double_prefix_found=false
for service in "${SERVICES[@]}"; do
    service_path="services/$service"
    if [ -d "$service_path" ]; then
        # Check for any remaining @Controller('api/...) patterns
        if find "$service_path/src" -name "*.controller.ts" -exec grep -l "@Controller('api/" {} \; 2>/dev/null | head -1 | grep -q .; then
            log_error "❌ Double prefix still found in $service"
            double_prefix_found=true
        else
            log_success "✅ $service verified clean"
        fi
    fi
done

echo ""
log_info "📊 SUMMARY"
echo "=========="
log_info "Services processed: ${#SERVICES[@]}"
log_info "Services fixed: $total_services_fixed"

if [ "$double_prefix_found" = true ]; then
    log_error "❌ Some services still have double API prefixes"
    exit 1
else
    log_success "✅ All services verified clean - no double API prefixes found!"
fi

echo ""
log_success "🎉 Double API Prefix Fix Complete!"
log_info "Next steps:"
log_info "1. Test each service individually"
log_info "2. Update API Gateway health check paths"
log_info "3. Restart all services"

echo ""
