import { Controller, Get, Res, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';

@ApiTags('Twitter Auth')
@Controller('auth/twitter')
export class TwitterAuthController {

  @Get('login')
  @ApiOperation({ summary: 'Initiate Twitter OAuth flow (Mock)' })
  @ApiResponse({ status: 302, description: 'Redirects to frontend with mock success' })
  async login(@Res() res: Response): Promise<void> {
    try {
      // Mock Twitter OAuth - simulate successful authentication
      const mockTwitterData = {
        twitterId: 'mock_twitter_id_' + Date.now(),
        screenName: 'MockUser',
        accessToken: 'mock_access_token_' + Date.now(),
        profileImageUrl: 'https://via.placeholder.com/48x48/1DA1F2/FFFFFF?text=T'
      };

      // Redirect to frontend with mock success data
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3100';
      const callbackUrl = `${frontendUrl}/auth/twitter/callback?` +
        `twitterId=${mockTwitterData.twitterId}&` +
        `screenName=${mockTwitterData.screenName}&` +
        `accessToken=${mockTwitterData.accessToken}&` +
        `profileImageUrl=${encodeURIComponent(mockTwitterData.profileImageUrl)}`;

      console.log('🐦 Mock Twitter OAuth - Redirecting to:', callbackUrl);
      res.redirect(callbackUrl);
    } catch (error) {
      console.error('❌ Mock Twitter OAuth error:', error);
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3100';
      res.redirect(`${frontendUrl}/auth/error?message=${encodeURIComponent('Mock Twitter authentication failed')}`);
    }
  }

  @Get('callback')
  @ApiOperation({ summary: 'Handle Twitter OAuth callback (Mock)' })
  @ApiResponse({ status: 302, description: 'Redirects to frontend with success or error' })
  async callback(
    @Query('oauth_token') oauthToken: string,
    @Query('oauth_verifier') oauthVerifier: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      // Mock callback processing
      const mockUserData = {
        twitterId: 'mock_twitter_id_callback',
        screenName: 'MockCallbackUser',
        accessToken: 'mock_callback_token_' + Date.now(),
        profileImageUrl: 'https://via.placeholder.com/48x48/1DA1F2/FFFFFF?text=CB'
      };

      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3100';
      const successUrl = `${frontendUrl}/auth/twitter/callback?` +
        `twitterId=${mockUserData.twitterId}&` +
        `screenName=${mockUserData.screenName}&` +
        `accessToken=${mockUserData.accessToken}&` +
        `profileImageUrl=${encodeURIComponent(mockUserData.profileImageUrl)}`;

      console.log('🐦 Mock Twitter OAuth Callback - Redirecting to:', successUrl);
      res.redirect(successUrl);
    } catch (error) {
      console.error('❌ Mock Twitter OAuth callback error:', error);
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3100';
      res.redirect(`${frontendUrl}/auth/error?message=${encodeURIComponent('Mock Twitter callback failed')}`);
    }
  }

  @Get('status')
  @ApiOperation({ summary: 'Check Twitter connection status (Mock)' })
  @ApiResponse({ status: 200, description: 'Returns Twitter connection status' })
  async getStatus(): Promise<any> {
    return {
      connected: true,
      mock: true,
      message: 'Mock Twitter OAuth is enabled for development'
    };
  }
}
