import { IsString, IsNotEmpty, IsOptional, IsEnum, IsDecimal, IsDateString, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ListingType } from '../entities/nft-listing.entity';

export class CreateListingDto {
  @ApiProperty({ description: 'NFT ID to list for sale' })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  nftId: string;

  @ApiProperty({ description: 'Token ID on blockchain' })
  @IsString()
  @IsNotEmpty()
  tokenId: string;

  @ApiProperty({ description: 'Smart contract address' })
  @IsString()
  @IsNotEmpty()
  contractAddress: string;

  @ApiProperty({ description: 'Listing price in ETH/MATIC' })
  @IsString()
  @IsNotEmpty()
  price: string;

  @ApiProperty({ description: 'Currency type', default: 'ETH' })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty({ description: 'Listing type', enum: ListingType, default: ListingType.FIXED_PRICE })
  @IsEnum(ListingType)
  @IsOptional()
  listingType?: ListingType;

  @ApiProperty({ description: 'Listing description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Listing expiration date (ISO string)', required: false })
  @IsDateString()
  @IsOptional()
  expiresAt?: string;

  @ApiProperty({ description: 'Platform fee percentage', required: false })
  @IsString()
  @IsOptional()
  platformFeePercentage?: string;

  @ApiProperty({ description: 'Creator royalty percentage', required: false })
  @IsString()
  @IsOptional()
  royaltyPercentage?: string;

  @ApiProperty({ description: 'Original creator ID for royalties', required: false })
  @IsString()
  @IsOptional()
  originalCreatorId?: string;
}
