import { Controller, Get, Post, Put, Body, Param, Query, Headers, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { CampaignService } from '../services/campaign.service';
import { CreateCampaignDto } from '../dto/create-campaign.dto';
import { JoinCampaignDto, UpdateCampaignStatusDto, UpdateParticipantScoreDto } from '../dto/join-campaign.dto';
import { ProcessAnalysisResultsDto } from '../dto/analysis-integration.dto';
import { Campaign } from '../entities/campaign.entity';
import { CampaignParticipant } from '../entities/campaign-participant.entity';

@ApiTags('Campaigns')
@Controller('campaigns')
export class CampaignController {
  constructor(private readonly campaignService: CampaignService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new campaign (Project owners only)' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Campaign created successfully', type: Campaign })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized - Project owner access required' })
  @ApiBearerAuth()
  async createCampaign(
    @Headers('x-user-id') projectOwnerId: string,
    @Body() createCampaignDto: CreateCampaignDto,
  ): Promise<Campaign> {
    return await this.campaignService.createCampaign(projectOwnerId, createCampaignDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all active campaigns (Public)' })
  @ApiResponse({ status: HttpStatus.OK, description: 'List of active campaigns', type: [Campaign] })
  async getActiveCampaigns(): Promise<Campaign[]> {
    return await this.campaignService.getActiveCampaigns();
  }

  @Get('my-campaigns')
  @ApiOperation({ summary: 'Get campaigns by project owner (Project owners only)' })
  @ApiResponse({ status: HttpStatus.OK, description: 'List of owner campaigns', type: [Campaign] })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized - Project owner access required' })
  @ApiBearerAuth()
  async getMyCampaigns(
    @Headers('x-user-id') projectOwnerId: string,
  ): Promise<Campaign[]> {
    return await this.campaignService.getCampaignsByOwner(projectOwnerId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get campaign by ID (Public)' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Campaign details', type: Campaign })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Campaign not found' })
  async getCampaignById(@Param('id') id: string): Promise<Campaign> {
    return await this.campaignService.getCampaignById(id);
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update campaign status (Project owners only)' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Campaign status updated', type: Campaign })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Campaign not found' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Not authorized to update this campaign' })
  @ApiBearerAuth()
  async updateCampaignStatus(
    @Param('id') id: string,
    @Headers('x-user-id') projectOwnerId: string,
    @Body() updateStatusDto: UpdateCampaignStatusDto,
  ): Promise<Campaign> {
    return await this.campaignService.updateCampaignStatus(id, projectOwnerId, updateStatusDto.status as any);
  }

  @Post(':id/join')
  @ApiOperation({ summary: 'Join a campaign (Users only)' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Successfully joined campaign', type: CampaignParticipant })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Cannot join campaign (already joined, campaign full, etc.)' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Campaign not found' })
  @ApiBearerAuth()
  async joinCampaign(
    @Param('id') campaignId: string,
    @Body() joinCampaignDto: JoinCampaignDto,
  ): Promise<CampaignParticipant> {
    return await this.campaignService.joinCampaign(
      campaignId,
      joinCampaignDto.userId,
      joinCampaignDto.twitterUsername,
      joinCampaignDto.twitterUserId,
    );
  }

  @Get(':id/participants')
  @ApiOperation({ summary: 'Get campaign participants (Project owners only)' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'List of campaign participants', type: [CampaignParticipant] })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Not authorized to view participants' })
  @ApiBearerAuth()
  async getCampaignParticipants(
    @Param('id') campaignId: string,
    @Headers('x-user-id') projectOwnerId: string,
  ): Promise<CampaignParticipant[]> {
    return await this.campaignService.getCampaignParticipants(campaignId, projectOwnerId);
  }

  @Get(':id/my-participation')
  @ApiOperation({ summary: 'Get user participation in campaign (Users only)' })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiQuery({ name: 'userId', description: 'User ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'User participation details', type: CampaignParticipant })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Participation not found' })
  @ApiBearerAuth()
  async getUserParticipation(
    @Param('id') campaignId: string,
    @Query('userId') userId: string,
  ): Promise<CampaignParticipant | null> {
    return await this.campaignService.getUserCampaignParticipation(campaignId, userId);
  }

  @Put('participant/score')
  @ApiOperation({ summary: 'Update participant score (Internal service call)' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Participant score updated', type: CampaignParticipant })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Participant not found' })
  async updateParticipantScore(
    @Body() updateScoreDto: UpdateParticipantScoreDto,
  ): Promise<CampaignParticipant> {
    return await this.campaignService.updateParticipantScore(
      updateScoreDto.campaignId,
      updateScoreDto.userId,
      updateScoreDto.newScore,
      updateScoreDto.reason,
    );
  }

  @Post('participant/process-analysis')
  @ApiOperation({ summary: 'Process analysis results and calculate score automatically (Internal service call)' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Analysis processed and score calculated', type: CampaignParticipant })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Participant not found' })
  async processAnalysisResults(
    @Body() processAnalysisDto: ProcessAnalysisResultsDto,
  ): Promise<CampaignParticipant> {
    return await this.campaignService.processAnalysisResults(
      processAnalysisDto.campaignId,
      processAnalysisDto.userId,
      processAnalysisDto.analysisResults,
      processAnalysisDto.analysisId,
    );
  }
}
