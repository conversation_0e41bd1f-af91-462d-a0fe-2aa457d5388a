import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Headers,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { OfferService } from '../services/offer.service';
import { CreateOfferDto } from '../dto/create-offer.dto';
import { Offer, OfferStatus } from '../entities/offer.entity';

@ApiTags('Marketplace - Offers')
@Controller('marketplace/offers')
export class OfferController {
  constructor(private readonly offerService: OfferService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new offer on an NFT listing' })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Offer created successfully', type: Offer })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid offer data' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Listing not found' })
  @ApiBearerAuth()
  async createOffer(
    @Body() createOfferDto: CreateOfferDto,
    @Headers('x-user-id') userId: string,
  ): Promise<Offer> {
    return await this.offerService.createOffer(createOfferDto, userId);
  }

  @Get('listing/:listingId')
  @ApiOperation({ summary: 'Get all offers for a specific listing' })
  @ApiParam({ name: 'listingId', description: 'Listing ID' })
  @ApiQuery({ name: 'status', required: false, enum: OfferStatus })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results per page' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of results to skip' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Offers retrieved successfully' })
  async getOffersForListing(
    @Param('listingId') listingId: string,
    @Query('status') status?: OfferStatus,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string,
  ) {
    const filters = {
      status,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
    };

    return await this.offerService.getOffersForListing(listingId, filters);
  }

  @Get('user/my-offers')
  @ApiOperation({ summary: 'Get user offers (made and received)' })
  @ApiQuery({ name: 'type', required: false, enum: ['made', 'received'], description: 'Filter by offer type' })
  @ApiQuery({ name: 'status', required: false, enum: OfferStatus })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results per page' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of results to skip' })
  @ApiResponse({ status: HttpStatus.OK, description: 'User offers retrieved successfully' })
  @ApiBearerAuth()
  async getUserOffers(
    @Headers('x-user-id') userId: string,
    @Query('type') type?: 'made' | 'received',
    @Query('status') status?: OfferStatus,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string,
  ) {
    const filters = {
      type,
      status,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
    };

    return await this.offerService.getUserOffers(userId, filters);
  }

  @Patch(':id/accept')
  @ApiOperation({ summary: 'Accept an offer (seller only)' })
  @ApiParam({ name: 'id', description: 'Offer ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Offer accepted successfully', type: Offer })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Offer not found' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Not authorized to accept this offer' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Offer cannot be accepted' })
  @ApiBearerAuth()
  async acceptOffer(
    @Param('id') id: string,
    @Headers('x-user-id') userId: string,
  ): Promise<Offer> {
    return await this.offerService.acceptOffer(id, userId);
  }

  @Patch(':id/reject')
  @ApiOperation({ summary: 'Reject an offer (seller only)' })
  @ApiParam({ name: 'id', description: 'Offer ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Offer rejected successfully', type: Offer })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Offer not found' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Not authorized to reject this offer' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Offer cannot be rejected' })
  @ApiBearerAuth()
  async rejectOffer(
    @Param('id') id: string,
    @Headers('x-user-id') userId: string,
    @Body() rejectData: { reason?: string },
  ): Promise<Offer> {
    return await this.offerService.rejectOffer(id, userId, rejectData.reason);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Cancel an offer (bidder only)' })
  @ApiParam({ name: 'id', description: 'Offer ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Offer cancelled successfully', type: Offer })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Offer not found' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Not authorized to cancel this offer' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Offer cannot be cancelled' })
  @ApiBearerAuth()
  async cancelOffer(
    @Param('id') id: string,
    @Headers('x-user-id') userId: string,
  ): Promise<Offer> {
    return await this.offerService.cancelOffer(id, userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get offer by ID' })
  @ApiParam({ name: 'id', description: 'Offer ID' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Offer retrieved successfully', type: Offer })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Offer not found' })
  async getOfferById(@Param('id') id: string): Promise<Offer> {
    // This would need to be implemented in the service
    // For now, we'll return a placeholder
    throw new Error('Method not implemented');
  }
}
