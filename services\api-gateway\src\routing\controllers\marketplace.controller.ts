import { Controller, Get, Post, Put, Patch, Delete, Body, Param, Query, Headers, Req, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ProxyService } from '../services/proxy.service';

@ApiTags('Marketplace')
@Controller('marketplace')
export class MarketplaceController {
  constructor(private readonly proxyService: ProxyService) {}

  // Helper method to handle proxy requests
  private async handleProxyRequest(
    req: Request,
    res: Response,
    serviceName: string,
    path: string,
    method: string,
    body?: any
  ) {
    try {
      const response = await this.proxyService.forwardRequest(
        serviceName,
        path,
        method,
        body,
        req.headers,
        req.query
      );

      return res.status(response.status).json(response.data);
    } catch (error) {
      return res.status(error.status || 500).json({
        message: error.message || 'Internal server error',
        service: serviceName
      });
    }
  }

  // ===== LISTING ENDPOINTS =====

  @Get('listings')
  @ApiOperation({ summary: 'Get all NFT listings with filters' })
  @ApiResponse({ status: 200, description: 'Listings retrieved successfully' })
  async getListings(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', '/api/marketplace/listings', 'GET');
  }

  @Post('listings')
  @ApiOperation({ summary: 'Create a new NFT listing' })
  @ApiResponse({ status: 201, description: 'Listing created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async createListing(@Body() createListingDto: any, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', '/api/marketplace/listings', 'POST', createListingDto);
  }

  @Get('listings/:id')
  @ApiOperation({ summary: 'Get listing by ID' })
  @ApiResponse({ status: 200, description: 'Listing details' })
  @ApiResponse({ status: 404, description: 'Listing not found' })
  async getListingById(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', `/api/marketplace/listings/${id}`, 'GET');
  }

  @Patch('listings/:id')
  @ApiOperation({ summary: 'Update listing' })
  @ApiResponse({ status: 200, description: 'Listing updated successfully' })
  @ApiResponse({ status: 404, description: 'Listing not found' })
  async updateListing(@Param('id') id: string, @Body() updateListingDto: any, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', `/api/marketplace/listings/${id}`, 'PATCH', updateListingDto);
  }

  @Delete('listings/:id')
  @ApiOperation({ summary: 'Cancel listing' })
  @ApiResponse({ status: 200, description: 'Listing cancelled successfully' })
  @ApiResponse({ status: 404, description: 'Listing not found' })
  async cancelListing(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', `/api/marketplace/listings/${id}`, 'DELETE');
  }

  // ===== TRANSACTION ENDPOINTS =====

  @Post('transactions/purchase')
  @ApiOperation({ summary: 'Purchase an NFT from a listing' })
  @ApiResponse({ status: 201, description: 'Purchase transaction created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid purchase request' })
  async purchaseNFT(@Body() purchaseDto: any, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', '/api/marketplace/transactions/purchase', 'POST', purchaseDto);
  }

  @Get('transactions/history')
  @ApiOperation({ summary: 'Get transaction history for user' })
  @ApiResponse({ status: 200, description: 'Transaction history retrieved successfully' })
  async getTransactionHistory(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', '/api/marketplace/transactions/history', 'GET');
  }

  @Patch('transactions/:id/confirm')
  @ApiOperation({ summary: 'Confirm a transaction with blockchain details' })
  @ApiResponse({ status: 200, description: 'Transaction confirmed successfully' })
  async confirmTransaction(@Param('id') id: string, @Body() confirmData: any, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', `/api/marketplace/transactions/${id}/confirm`, 'PATCH', confirmData);
  }

  // ===== OFFER ENDPOINTS =====

  @Post('offers')
  @ApiOperation({ summary: 'Create a new offer on an NFT listing' })
  @ApiResponse({ status: 201, description: 'Offer created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid offer data' })
  async createOffer(@Body() createOfferDto: any, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', '/api/marketplace/offers', 'POST', createOfferDto);
  }

  @Get('offers/listing/:listingId')
  @ApiOperation({ summary: 'Get all offers for a specific listing' })
  @ApiResponse({ status: 200, description: 'Offers retrieved successfully' })
  async getOffersForListing(@Param('listingId') listingId: string, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', `/api/marketplace/offers/listing/${listingId}`, 'GET');
  }

  @Get('offers/user/my-offers')
  @ApiOperation({ summary: 'Get user offers (made and received)' })
  @ApiResponse({ status: 200, description: 'User offers retrieved successfully' })
  async getUserOffers(@Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', '/api/marketplace/offers/user/my-offers', 'GET');
  }

  @Patch('offers/:id/accept')
  @ApiOperation({ summary: 'Accept an offer (seller only)' })
  @ApiResponse({ status: 200, description: 'Offer accepted successfully' })
  async acceptOffer(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', `/api/marketplace/offers/${id}/accept`, 'PATCH');
  }

  @Patch('offers/:id/reject')
  @ApiOperation({ summary: 'Reject an offer (seller only)' })
  @ApiResponse({ status: 200, description: 'Offer rejected successfully' })
  async rejectOffer(@Param('id') id: string, @Body() rejectData: any, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', `/api/marketplace/offers/${id}/reject`, 'PATCH', rejectData);
  }

  @Delete('offers/:id')
  @ApiOperation({ summary: 'Cancel an offer (bidder only)' })
  @ApiResponse({ status: 200, description: 'Offer cancelled successfully' })
  async cancelOffer(@Param('id') id: string, @Req() req: Request, @Res() res: Response) {
    return this.handleProxyRequest(req, res, 'marketplace-service', `/api/marketplace/offers/${id}`, 'DELETE');
  }
}
