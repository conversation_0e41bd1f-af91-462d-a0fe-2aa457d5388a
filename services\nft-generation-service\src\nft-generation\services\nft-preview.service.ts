import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Nft } from '../entities/nft.entity';
import { TemplateManagementService } from './template-management.service';
import { ImageGenerationService } from './image-generation.service';
import { MetadataService } from './metadata.service';

export interface PreviewNftDto {
  userId: string;
  campaignId: string;
  twitterHandle: string;
  analysisData: any;
  campaignConfiguration: any;
  currentScore: number;
  templateId?: string;
}

export interface NFTPreviewResponse {
  previewId: string;
  imageUrl: string;
  metadata: any;
  rarity: string;
  template: any;
  attributes: any;
  estimatedRarity: string;
  previewTimestamp: string;
}

export interface NFTGalleryFilters {
  userId?: string;
  campaignId?: string;
  rarity?: string;
  templateId?: string;
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
  offset?: number;
}

@Injectable()
export class NftPreviewService {
  constructor(
    @InjectRepository(Nft)
    private nftRepository: Repository<Nft>,
    private templateManagementService: TemplateManagementService,
    private imageGenerationService: ImageGenerationService,
    private metadataService: MetadataService,
  ) {}

  /**
   * Generate NFT preview without saving to database
   */
  async generatePreview(previewDto: PreviewNftDto): Promise<NFTPreviewResponse> {
    try {
      // Determine rarity for preview
      const rarity = await this.estimateRarity(
        previewDto.currentScore,
        previewDto.campaignConfiguration,
        previewDto.campaignId
      );

      // Get template for preview
      const template = previewDto.templateId 
        ? await this.templateManagementService.getTemplate(previewDto.templateId)
        : await this.templateManagementService.getTemplateByRarity(previewDto.campaignId, rarity);

      // Generate preview image (temporary)
      const previewImageUrl = await this.generatePreviewImage(
        previewDto.analysisData,
        previewDto.campaignConfiguration,
        template,
        rarity
      );

      // Generate preview metadata
      const previewMetadata = await this.generatePreviewMetadata(
        previewDto,
        template,
        rarity,
        previewImageUrl
      );

      // Generate preview attributes
      const previewAttributes = await this.generatePreviewAttributes(
        previewDto.analysisData,
        template,
        rarity
      );

      const previewId = `preview_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      return {
        previewId,
        imageUrl: previewImageUrl,
        metadata: previewMetadata,
        rarity,
        template: {
          id: template.id,
          name: template.name,
          type: template.templateType,
          category: template.category
        },
        attributes: previewAttributes,
        estimatedRarity: rarity,
        previewTimestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating NFT preview:', error);
      throw new Error(`Failed to generate NFT preview: ${error.message}`);
    }
  }

  /**
   * Get NFT gallery with filters and pagination
   */
  async getNFTGallery(filters: NFTGalleryFilters = {}): Promise<{
    nfts: Nft[];
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  }> {
    const queryBuilder = this.nftRepository.createQueryBuilder('nft');

    // Apply filters
    if (filters.userId) {
      queryBuilder.andWhere('nft.userId = :userId', { userId: filters.userId });
    }

    if (filters.campaignId) {
      queryBuilder.andWhere('nft.campaignId = :campaignId', { campaignId: filters.campaignId });
    }

    if (filters.rarity) {
      queryBuilder.andWhere('nft.rarity = :rarity', { rarity: filters.rarity });
    }

    if (filters.templateId) {
      queryBuilder.andWhere('nft.metadata->>\'template\'->>\'id\' = :templateId', { 
        templateId: filters.templateId 
      });
    }

    if (filters.dateFrom) {
      queryBuilder.andWhere('nft.createdAt >= :dateFrom', { dateFrom: filters.dateFrom });
    }

    if (filters.dateTo) {
      queryBuilder.andWhere('nft.createdAt <= :dateTo', { dateTo: filters.dateTo });
    }

    // Only active NFTs
    queryBuilder.andWhere('nft.isActive = :isActive', { isActive: true });

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const limit = filters.limit || 20;
    const offset = filters.offset || 0;
    const page = Math.floor(offset / limit) + 1;

    queryBuilder
      .orderBy('nft.createdAt', 'DESC')
      .limit(limit)
      .offset(offset);

    const nfts = await queryBuilder.getMany();

    return {
      nfts,
      total,
      page,
      limit,
      hasMore: offset + nfts.length < total
    };
  }

  /**
   * Get NFT details with enhanced visualization data
   */
  async getNFTDetails(nftId: string): Promise<any> {
    const nft = await this.nftRepository.findOne({
      where: { id: nftId, isActive: true }
    });

    if (!nft) {
      throw new NotFoundException('NFT not found');
    }

    // Get template information if available
    let templateInfo = null;
    if (nft.metadata?.template?.id) {
      try {
        templateInfo = await this.templateManagementService.getTemplate(nft.metadata.template.id);
      } catch (error) {
        console.warn('Template not found for NFT:', nft.metadata.template.id);
      }
    }

    // Enhanced visualization data
    const visualizationData = {
      ...nft,
      template: templateInfo,
      rarityInfo: this.getRarityInfo(nft.rarity),
      attributeAnalysis: this.analyzeAttributes(nft.attributes),
      visualElements: this.extractVisualElements(nft.metadata),
      shareableUrl: this.generateShareableUrl(nft.id),
      downloadUrls: this.generateDownloadUrls(nft.imageUrl)
    };

    return visualizationData;
  }

  /**
   * Estimate rarity for preview
   */
  private async estimateRarity(
    score: number,
    campaignConfig: any,
    campaignId: string
  ): Promise<string> {
    try {
      const campaignTemplates = await this.templateManagementService.getCampaignTemplates(campaignId);

      if (campaignTemplates.length > 0) {
        const availableRarities = campaignTemplates.map(t => t.rarity);
        const enhancedScore = this.calculateEnhancedScore(score, campaignConfig);

        if (availableRarities.includes('Legendary') && enhancedScore >= 90) return 'Legendary';
        if (availableRarities.includes('Epic') && enhancedScore >= 75) return 'Epic';
        if (availableRarities.includes('Rare') && enhancedScore >= 60) return 'Rare';
        if (availableRarities.includes('Uncommon') && enhancedScore >= 40) return 'Uncommon';

        return 'Common';
      }
    } catch (error) {
      console.warn('Failed to estimate rarity from templates:', error.message);
    }

    return this.calculateBasicRarity(score, campaignConfig);
  }

  /**
   * Calculate enhanced score for preview
   */
  private calculateEnhancedScore(baseScore: number, campaignConfig: any): number {
    let enhancedScore = baseScore * 0.7;
    const bonusFactors = campaignConfig.bonusFactors || {};

    if (bonusFactors.highEngagement) enhancedScore += 10;
    if (bonusFactors.verified) enhancedScore += 10;
    if (bonusFactors.accountAge > 365) enhancedScore += 5;
    if (bonusFactors.followerRatio > 2) enhancedScore += 5;

    return Math.min(enhancedScore, 100);
  }

  /**
   * Calculate basic rarity (fallback)
   */
  private calculateBasicRarity(score: number, campaignConfig: any): string {
    const thresholds = campaignConfig.scoreThresholds || {};

    if (score >= (thresholds.legendary || 90)) return 'Legendary';
    if (score >= (thresholds.rare || 60)) return 'Rare';
    return 'Common';
  }

  /**
   * Generate preview image
   */
  private async generatePreviewImage(
    analysisData: any,
    campaignConfig: any,
    template: any,
    rarity: string
  ): Promise<string> {
    const imageUrl = await this.imageGenerationService.generateImageWithTemplate(
      analysisData,
      campaignConfig,
      template,
      rarity
    );

    return `${imageUrl}?preview=true&timestamp=${Date.now()}`;
  }

  /**
   * Generate preview metadata
   */
  private async generatePreviewMetadata(
    previewDto: PreviewNftDto,
    template: any,
    rarity: string,
    imageUrl: string
  ): Promise<any> {
    const metadata = await this.metadataService.generateEnhancedMetadata(
      previewDto as any,
      template,
      rarity,
      imageUrl
    );

    metadata.preview = true;
    metadata.preview_timestamp = new Date().toISOString();
    metadata.preview_note = 'This is a preview. Actual NFT may vary slightly.';

    return metadata;
  }

  /**
   * Generate preview attributes
   */
  private async generatePreviewAttributes(
    analysisData: any,
    template: any,
    rarity: string
  ): Promise<any> {
    const baseAttributes = {
      rarity,
      followers: analysisData.followers || 0,
      following: analysisData.following || 0,
      tweets: analysisData.tweets || 0,
      engagement: analysisData.engagement || 0,
      verified: analysisData.verified || false,
    };

    if (template.dynamicAttributes) {
      const dynamicAttrs = this.processDynamicAttributes(analysisData, template.dynamicAttributes);
      Object.assign(baseAttributes, dynamicAttrs);
    }

    return baseAttributes;
  }

  /**
   * Process dynamic attributes for preview
   */
  private processDynamicAttributes(analysisData: any, dynamicConfig: any): any {
    const dynamicAttributes = {};

    if (!dynamicConfig || !dynamicConfig.rules) {
      return dynamicAttributes;
    }

    for (const rule of dynamicConfig.rules) {
      try {
        const value = this.evaluateAttributeRule(rule, analysisData);
        if (value !== null && value !== undefined) {
          dynamicAttributes[rule.attributeName] = value;
        }
      } catch (error) {
        console.warn(`Failed to evaluate dynamic attribute: ${rule.attributeName}`, error);
      }
    }

    return dynamicAttributes;
  }

  /**
   * Evaluate attribute rule for preview
   */
  private evaluateAttributeRule(rule: any, analysisData: any): any {
    const { source, type, valueMapping } = rule;

    let sourceValue = analysisData[source];
    if (sourceValue === undefined || sourceValue === null) {
      return rule.defaultValue || null;
    }

    if (valueMapping) {
      return this.applyValueMapping(sourceValue, valueMapping, type);
    }

    return this.processValueByType(sourceValue, type);
  }

  /**
   * Apply value mapping for preview
   */
  private applyValueMapping(sourceValue: any, valueMapping: any, type: string): any {
    if (valueMapping.ranges) {
      for (const range of valueMapping.ranges) {
        if (sourceValue >= range.min && sourceValue <= range.max) {
          return range.value;
        }
      }
    }

    if (valueMapping.direct && valueMapping.direct[sourceValue]) {
      return valueMapping.direct[sourceValue];
    }

    return this.processValueByType(sourceValue, type);
  }

  /**
   * Process value by type for preview
   */
  private processValueByType(value: any, type: string): any {
    switch (type) {
      case 'string': return String(value);
      case 'number': return Number(value);
      case 'boolean': return Boolean(value);
      case 'percentage': return `${Math.round(Number(value) * 100) / 100}%`;
      case 'tier': return this.calculateTier(Number(value));
      default: return value;
    }
  }

  /**
   * Calculate tier for preview
   */
  private calculateTier(value: number): string {
    if (value >= 90) return 'S';
    if (value >= 80) return 'A';
    if (value >= 70) return 'B';
    if (value >= 60) return 'C';
    if (value >= 50) return 'D';
    return 'E';
  }

  /**
   * Get rarity information for visualization
   */
  private getRarityInfo(rarity: string): any {
    const rarityMap = {
      'Legendary': { color: '#FFD700', rank: 1, description: 'Extremely rare and valuable' },
      'Epic': { color: '#9932CC', rank: 2, description: 'Very rare with special features' },
      'Rare': { color: '#4169E1', rank: 3, description: 'Uncommon with enhanced attributes' },
      'Uncommon': { color: '#32CD32', rank: 4, description: 'Above average quality' },
      'Common': { color: '#808080', rank: 5, description: 'Standard quality NFT' }
    };

    return rarityMap[rarity] || rarityMap['Common'];
  }

  /**
   * Analyze attributes for visualization
   */
  private analyzeAttributes(attributes: any): any {
    if (!attributes) return {};

    return {
      totalAttributes: Object.keys(attributes).length,
      socialMetrics: {
        followers: attributes.followers || 0,
        engagement: attributes.engagement || 0,
        verified: attributes.verified || false
      },
      rarityFactors: this.extractRarityFactors(attributes),
      uniqueTraits: this.countUniqueTraits(attributes)
    };
  }

  /**
   * Extract visual elements from metadata
   */
  private extractVisualElements(metadata: any): any {
    if (!metadata) return {};

    return {
      templateInfo: metadata.template || {},
      generationMethod: metadata.generation?.method || 'standard',
      visualEffects: this.getVisualEffects(metadata),
      colorScheme: this.getColorScheme(metadata)
    };
  }

  /**
   * Generate shareable URL for NFT
   */
  private generateShareableUrl(nftId: string): string {
    const baseUrl = process.env.PLATFORM_BASE_URL || 'https://social-nft-platform.com';
    return `${baseUrl}/nft/${nftId}`;
  }

  /**
   * Generate download URLs for different formats
   */
  private generateDownloadUrls(imageUrl: string): any {
    const baseUrl = imageUrl.replace(/\?.*$/, ''); // Remove query parameters

    return {
      original: imageUrl,
      high_res: `${baseUrl}?format=high&quality=100`,
      medium_res: `${baseUrl}?format=medium&quality=80`,
      thumbnail: `${baseUrl}?format=thumb&size=200x200`,
      metadata_json: `${baseUrl.replace(/\.[^.]+$/, '.json')}`
    };
  }

  /**
   * Extract rarity factors from attributes
   */
  private extractRarityFactors(attributes: any): any {
    return {
      socialScore: this.calculateSocialScore(attributes),
      engagementLevel: this.getEngagementLevel(attributes.engagement || 0),
      followerTier: this.getFollowerTier(attributes.followers || 0),
      verificationBonus: attributes.verified ? 'verified' : 'unverified'
    };
  }

  /**
   * Count unique traits in attributes
   */
  private countUniqueTraits(attributes: any): number {
    const standardTraits = ['rarity', 'followers', 'following', 'tweets', 'engagement', 'verified'];
    const uniqueTraits = Object.keys(attributes).filter(key => !standardTraits.includes(key));
    return uniqueTraits.length;
  }

  /**
   * Get visual effects based on metadata
   */
  private getVisualEffects(metadata: any): string[] {
    const effects = [];

    if (metadata.rarity?.template_rarity === 'Legendary') {
      effects.push('golden_glow', 'particle_effects');
    } else if (metadata.rarity?.template_rarity === 'Epic') {
      effects.push('purple_aura');
    } else if (metadata.rarity?.template_rarity === 'Rare') {
      effects.push('blue_shimmer');
    }

    if (metadata.generation?.template_type === 'dynamic') {
      effects.push('dynamic_elements');
    }

    return effects;
  }

  /**
   * Get color scheme based on metadata
   */
  private getColorScheme(metadata: any): any {
    const rarity = metadata.rarity?.template_rarity || 'Common';
    const rarityInfo = this.getRarityInfo(rarity);

    return {
      primary: rarityInfo.color,
      secondary: this.getSecondaryColor(rarityInfo.color),
      accent: this.getAccentColor(rarityInfo.color)
    };
  }

  /**
   * Calculate social score from attributes
   */
  private calculateSocialScore(attributes: any): number {
    const followers = attributes.followers || 0;
    const engagement = attributes.engagement || 0;
    const verified = attributes.verified || false;

    let score = 0;

    // Follower score (0-40 points)
    if (followers >= 1000000) score += 40;
    else if (followers >= 100000) score += 30;
    else if (followers >= 10000) score += 20;
    else if (followers >= 1000) score += 10;

    // Engagement score (0-40 points)
    score += Math.min(engagement * 400, 40);

    // Verification bonus (0-20 points)
    if (verified) score += 20;

    return Math.round(score);
  }

  /**
   * Get engagement level description
   */
  private getEngagementLevel(engagement: number): string {
    if (engagement >= 0.1) return 'exceptional';
    if (engagement >= 0.05) return 'high';
    if (engagement >= 0.02) return 'moderate';
    if (engagement >= 0.01) return 'low';
    return 'minimal';
  }

  /**
   * Get follower tier description
   */
  private getFollowerTier(followers: number): string {
    if (followers >= 1000000) return 'mega_influencer';
    if (followers >= 100000) return 'macro_influencer';
    if (followers >= 10000) return 'micro_influencer';
    if (followers >= 1000) return 'nano_influencer';
    return 'emerging_creator';
  }

  /**
   * Get secondary color for color scheme
   */
  private getSecondaryColor(primaryColor: string): string {
    const colorMap = {
      '#FFD700': '#FFA500', // Gold -> Orange
      '#9932CC': '#8A2BE2', // Purple -> Blue Violet
      '#4169E1': '#1E90FF', // Blue -> Dodger Blue
      '#32CD32': '#228B22', // Green -> Forest Green
      '#808080': '#696969'  // Gray -> Dim Gray
    };

    return colorMap[primaryColor] || '#696969';
  }

  /**
   * Get accent color for color scheme
   */
  private getAccentColor(primaryColor: string): string {
    const colorMap = {
      '#FFD700': '#FFFF00', // Gold -> Yellow
      '#9932CC': '#DA70D6', // Purple -> Orchid
      '#4169E1': '#87CEEB', // Blue -> Sky Blue
      '#32CD32': '#90EE90', // Green -> Light Green
      '#808080': '#D3D3D3'  // Gray -> Light Gray
    };

    return colorMap[primaryColor] || '#D3D3D3';
  }
}
