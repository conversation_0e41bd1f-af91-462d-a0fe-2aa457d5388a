import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Offer, OfferStatus } from '../entities/offer.entity';
import { NFTListing, ListingStatus } from '../entities/nft-listing.entity';
import { CreateOfferDto } from '../dto/create-offer.dto';

@Injectable()
export class OfferService {
  constructor(
    @InjectRepository(Offer)
    private readonly offerRepository: Repository<Offer>,
    @InjectRepository(NFTListing)
    private readonly listingRepository: Repository<NFTListing>,
    private readonly dataSource: DataSource,
  ) {}

  async createOffer(createOfferDto: CreateOfferDto, bidderId: string): Promise<Offer> {
    // Find and validate listing
    const listing = await this.listingRepository.findOne({
      where: { id: createOfferDto.listingId },
    });

    if (!listing) {
      throw new NotFoundException('Listing not found');
    }

    if (listing.status !== ListingStatus.ACTIVE) {
      throw new BadRequestException('Cannot make offers on inactive listings');
    }

    if (listing.sellerId === bidderId) {
      throw new BadRequestException('Cannot make offers on your own listings');
    }

    // Validate offer amount
    const offerAmount = parseFloat(createOfferDto.amount);
    if (offerAmount <= 0) {
      throw new BadRequestException('Offer amount must be greater than 0');
    }

    // Set default expiration if not provided (24 hours)
    const expiresAt = createOfferDto.expiresAt 
      ? new Date(createOfferDto.expiresAt)
      : new Date(Date.now() + 24 * 60 * 60 * 1000);

    // Check if user already has an active offer on this listing
    const existingOffer = await this.offerRepository.findOne({
      where: {
        listingId: createOfferDto.listingId,
        bidderId,
        status: OfferStatus.ACTIVE,
      },
    });

    if (existingOffer) {
      throw new BadRequestException('You already have an active offer on this listing');
    }

    const offer = this.offerRepository.create({
      ...createOfferDto,
      bidderId,
      sellerId: listing.sellerId,
      nftId: listing.nftId,
      expiresAt,
      status: OfferStatus.ACTIVE,
      currency: createOfferDto.currency || 'ETH',
    });

    return await this.offerRepository.save(offer);
  }

  async getOffersForListing(listingId: string, filters?: {
    status?: OfferStatus;
    limit?: number;
    offset?: number;
  }): Promise<{ offers: Offer[]; total: number }> {
    const query = this.offerRepository.createQueryBuilder('offer')
      .where('offer.listingId = :listingId', { listingId });

    // Apply filters
    if (filters?.status) {
      query.andWhere('offer.status = :status', { status: filters.status });
    }

    // Add pagination
    if (filters?.limit) {
      query.limit(filters.limit);
    }

    if (filters?.offset) {
      query.offset(filters.offset);
    }

    // Order by amount (highest first), then by creation date
    query.orderBy('CAST(offer.amount AS DECIMAL)', 'DESC')
         .addOrderBy('offer.createdAt', 'DESC');

    const [offers, total] = await query.getManyAndCount();

    return { offers, total };
  }

  async getUserOffers(userId: string, filters?: {
    type?: 'made' | 'received';
    status?: OfferStatus;
    limit?: number;
    offset?: number;
  }): Promise<{ offers: Offer[]; total: number }> {
    const query = this.offerRepository.createQueryBuilder('offer')
      .leftJoinAndSelect('offer.listing', 'listing');

    // Filter by user role
    if (filters?.type === 'made') {
      query.where('offer.bidderId = :userId', { userId });
    } else if (filters?.type === 'received') {
      query.where('offer.sellerId = :userId', { userId });
    } else {
      query.where('(offer.bidderId = :userId OR offer.sellerId = :userId)', { userId });
    }

    // Apply additional filters
    if (filters?.status) {
      query.andWhere('offer.status = :status', { status: filters.status });
    }

    // Add pagination
    if (filters?.limit) {
      query.limit(filters.limit);
    }

    if (filters?.offset) {
      query.offset(filters.offset);
    }

    // Order by creation date (newest first)
    query.orderBy('offer.createdAt', 'DESC');

    const [offers, total] = await query.getManyAndCount();

    return { offers, total };
  }

  async acceptOffer(offerId: string, sellerId: string): Promise<Offer> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const offer = await queryRunner.manager.findOne(Offer, {
        where: { id: offerId },
        relations: ['listing'],
      });

      if (!offer) {
        throw new NotFoundException('Offer not found');
      }

      if (offer.sellerId !== sellerId) {
        throw new ForbiddenException('You can only accept offers on your own listings');
      }

      if (offer.status !== OfferStatus.ACTIVE) {
        throw new BadRequestException('Offer is not active');
      }

      // Check if offer has expired
      if (new Date() > offer.expiresAt) {
        offer.status = OfferStatus.EXPIRED;
        await queryRunner.manager.save(Offer, offer);
        throw new BadRequestException('Offer has expired');
      }

      // Accept the offer
      offer.status = OfferStatus.ACCEPTED;
      offer.acceptedAt = new Date();
      await queryRunner.manager.save(Offer, offer);

      // Reject all other active offers for this listing
      await queryRunner.manager.update(
        Offer,
        {
          listingId: offer.listingId,
          status: OfferStatus.ACTIVE,
          id: { $ne: offerId } as any,
        },
        {
          status: OfferStatus.REJECTED,
          rejectedAt: new Date(),
          rejectionReason: 'Another offer was accepted',
        }
      );

      await queryRunner.commitTransaction();
      return offer;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async rejectOffer(offerId: string, sellerId: string, reason?: string): Promise<Offer> {
    const offer = await this.offerRepository.findOne({
      where: { id: offerId },
    });

    if (!offer) {
      throw new NotFoundException('Offer not found');
    }

    if (offer.sellerId !== sellerId) {
      throw new ForbiddenException('You can only reject offers on your own listings');
    }

    if (offer.status !== OfferStatus.ACTIVE) {
      throw new BadRequestException('Offer is not active');
    }

    offer.status = OfferStatus.REJECTED;
    offer.rejectedAt = new Date();
    offer.rejectionReason = reason;

    return await this.offerRepository.save(offer);
  }

  async cancelOffer(offerId: string, bidderId: string): Promise<Offer> {
    const offer = await this.offerRepository.findOne({
      where: { id: offerId },
    });

    if (!offer) {
      throw new NotFoundException('Offer not found');
    }

    if (offer.bidderId !== bidderId) {
      throw new ForbiddenException('You can only cancel your own offers');
    }

    if (offer.status !== OfferStatus.ACTIVE) {
      throw new BadRequestException('Offer is not active');
    }

    offer.status = OfferStatus.CANCELLED;

    return await this.offerRepository.save(offer);
  }
}
