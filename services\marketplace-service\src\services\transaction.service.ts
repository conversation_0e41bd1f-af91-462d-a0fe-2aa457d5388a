import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Transaction, TransactionStatus, TransactionType } from '../entities/transaction.entity';
import { NFTListing, ListingStatus } from '../entities/nft-listing.entity';
import { PurchaseNFTDto } from '../dto/purchase-nft.dto';

@Injectable()
export class TransactionService {
  constructor(
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    @InjectRepository(NFTListing)
    private readonly listingRepository: Repository<NFTListing>,
    private readonly dataSource: DataSource,
  ) {}

  async purchaseNFT(purchaseDto: PurchaseNFTDto, buyerId: string): Promise<Transaction> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Find and validate listing
      const listing = await queryRunner.manager.findOne(NFTListing, {
        where: { id: purchaseDto.listingId },
      });

      if (!listing) {
        throw new NotFoundException('Listing not found');
      }

      if (listing.status !== ListingStatus.ACTIVE) {
        throw new BadRequestException('Listing is not available for purchase');
      }

      if (listing.sellerId === buyerId) {
        throw new BadRequestException('Cannot purchase your own NFT');
      }

      // Validate price if provided
      if (purchaseDto.expectedPrice && listing.price !== purchaseDto.expectedPrice) {
        throw new BadRequestException('Price has changed. Please refresh and try again');
      }

      // Check if listing has expired
      if (listing.expiresAt && new Date() > listing.expiresAt) {
        listing.status = ListingStatus.EXPIRED;
        await queryRunner.manager.save(NFTListing, listing);
        throw new BadRequestException('Listing has expired');
      }

      // Calculate fees
      const amount = parseFloat(listing.price);
      const platformFeePercentage = parseFloat(listing.platformFeePercentage);
      const royaltyPercentage = parseFloat(listing.royaltyPercentage);

      const platformFee = (amount * platformFeePercentage) / 100;
      const royaltyFee = (amount * royaltyPercentage) / 100;
      const sellerAmount = amount - platformFee - royaltyFee;

      // Create transaction
      const transaction = queryRunner.manager.create(Transaction, {
        listingId: listing.id,
        nftId: listing.nftId,
        buyerId,
        sellerId: listing.sellerId,
        tokenId: listing.tokenId,
        contractAddress: listing.contractAddress,
        amount: listing.price,
        currency: listing.currency,
        platformFee: platformFee.toFixed(8),
        royaltyFee: royaltyFee.toFixed(8),
        sellerAmount: sellerAmount.toFixed(8),
        status: TransactionStatus.PENDING,
        transactionType: TransactionType.PURCHASE,
        blockchainTxHash: purchaseDto.paymentTxHash,
      });

      const savedTransaction = await queryRunner.manager.save(Transaction, transaction);

      // Update listing status to sold
      listing.status = ListingStatus.SOLD;
      await queryRunner.manager.save(NFTListing, listing);

      await queryRunner.commitTransaction();

      return savedTransaction;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async confirmTransaction(transactionId: string, blockchainTxHash: string, blockNumber?: number): Promise<Transaction> {
    const transaction = await this.transactionRepository.findOne({
      where: { id: transactionId },
    });

    if (!transaction) {
      throw new NotFoundException('Transaction not found');
    }

    if (transaction.status !== TransactionStatus.PENDING) {
      throw new BadRequestException('Transaction is not in pending status');
    }

    transaction.status = TransactionStatus.CONFIRMED;
    transaction.blockchainTxHash = blockchainTxHash;
    transaction.blockNumber = blockNumber;
    transaction.confirmedAt = new Date();

    return await this.transactionRepository.save(transaction);
  }

  async failTransaction(transactionId: string, reason?: string): Promise<Transaction> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const transaction = await queryRunner.manager.findOne(Transaction, {
        where: { id: transactionId },
      });

      if (!transaction) {
        throw new NotFoundException('Transaction not found');
      }

      if (transaction.status !== TransactionStatus.PENDING) {
        throw new BadRequestException('Transaction is not in pending status');
      }

      // Update transaction status
      transaction.status = TransactionStatus.FAILED;
      transaction.metadata = { ...transaction.metadata, failureReason: reason };
      await queryRunner.manager.save(Transaction, transaction);

      // Revert listing status back to active
      const listing = await queryRunner.manager.findOne(NFTListing, {
        where: { id: transaction.listingId },
      });

      if (listing && listing.status === ListingStatus.SOLD) {
        listing.status = ListingStatus.ACTIVE;
        await queryRunner.manager.save(NFTListing, listing);
      }

      await queryRunner.commitTransaction();
      return transaction;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getTransactionHistory(userId: string, filters?: {
    type?: 'buyer' | 'seller';
    status?: TransactionStatus;
    limit?: number;
    offset?: number;
  }): Promise<{ transactions: Transaction[]; total: number }> {
    const query = this.transactionRepository.createQueryBuilder('transaction')
      .leftJoinAndSelect('transaction.listing', 'listing');

    // Filter by user role
    if (filters?.type === 'buyer') {
      query.where('transaction.buyerId = :userId', { userId });
    } else if (filters?.type === 'seller') {
      query.where('transaction.sellerId = :userId', { userId });
    } else {
      query.where('(transaction.buyerId = :userId OR transaction.sellerId = :userId)', { userId });
    }

    // Apply additional filters
    if (filters?.status) {
      query.andWhere('transaction.status = :status', { status: filters.status });
    }

    // Add pagination
    if (filters?.limit) {
      query.limit(filters.limit);
    }

    if (filters?.offset) {
      query.offset(filters.offset);
    }

    // Order by creation date (newest first)
    query.orderBy('transaction.createdAt', 'DESC');

    const [transactions, total] = await query.getManyAndCount();

    return { transactions, total };
  }
}
