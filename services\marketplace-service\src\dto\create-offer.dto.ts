import { IsString, <PERSON>NotEmpty, <PERSON><PERSON>ptional, IsDateString, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOfferDto {
  @ApiProperty({ description: 'Listing ID to make offer on' })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  listingId: string;

  @ApiProperty({ description: 'Offer amount in ETH/MATIC' })
  @IsString()
  @IsNotEmpty()
  amount: string;

  @ApiProperty({ description: 'Currency type', default: 'ETH' })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty({ description: 'Optional message to seller', required: false })
  @IsString()
  @IsOptional()
  message?: string;

  @ApiProperty({ description: 'Offer expiration date (ISO string)', required: false })
  @IsDateString()
  @IsOptional()
  expiresAt?: string;
}
