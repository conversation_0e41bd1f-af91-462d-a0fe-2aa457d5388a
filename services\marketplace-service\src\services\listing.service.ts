import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NFTListing, ListingStatus } from '../entities/nft-listing.entity';
import { CreateListingDto } from '../dto/create-listing.dto';
import { UpdateListingDto } from '../dto/update-listing.dto';

@Injectable()
export class ListingService {
  constructor(
    @InjectRepository(NFTListing)
    private readonly listingRepository: Repository<NFTListing>,
  ) {}

  async createListing(createListingDto: CreateListingDto, sellerId: string): Promise<NFTListing> {
    // Validate price
    const price = parseFloat(createListingDto.price);
    if (price <= 0) {
      throw new BadRequestException('Price must be greater than 0');
    }

    // Set default expiration if not provided (30 days)
    const expiresAt = createListingDto.expiresAt 
      ? new Date(createListingDto.expiresAt)
      : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

    const listing = this.listingRepository.create({
      ...createListingDto,
      sellerId,
      expiresAt,
      status: ListingStatus.ACTIVE,
      platformFeePercentage: createListingDto.platformFeePercentage || '2.5',
      royaltyPercentage: createListingDto.royaltyPercentage || '5.0',
      currency: createListingDto.currency || 'ETH',
    });

    return await this.listingRepository.save(listing);
  }

  async findAllListings(filters?: {
    status?: ListingStatus;
    sellerId?: string;
    minPrice?: string;
    maxPrice?: string;
    currency?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ listings: NFTListing[]; total: number }> {
    const query = this.listingRepository.createQueryBuilder('listing');

    // Apply filters
    if (filters?.status) {
      query.andWhere('listing.status = :status', { status: filters.status });
    }

    if (filters?.sellerId) {
      query.andWhere('listing.sellerId = :sellerId', { sellerId: filters.sellerId });
    }

    if (filters?.minPrice) {
      query.andWhere('CAST(listing.price AS DECIMAL) >= :minPrice', { minPrice: filters.minPrice });
    }

    if (filters?.maxPrice) {
      query.andWhere('CAST(listing.price AS DECIMAL) <= :maxPrice', { maxPrice: filters.maxPrice });
    }

    if (filters?.currency) {
      query.andWhere('listing.currency = :currency', { currency: filters.currency });
    }

    // Add pagination
    if (filters?.limit) {
      query.limit(filters.limit);
    }

    if (filters?.offset) {
      query.offset(filters.offset);
    }

    // Order by creation date (newest first)
    query.orderBy('listing.createdAt', 'DESC');

    const [listings, total] = await query.getManyAndCount();

    return { listings, total };
  }

  async findListingById(id: string): Promise<NFTListing> {
    const listing = await this.listingRepository.findOne({
      where: { id },
    });

    if (!listing) {
      throw new NotFoundException(`Listing with ID ${id} not found`);
    }

    return listing;
  }

  async updateListing(id: string, updateListingDto: UpdateListingDto, userId: string): Promise<NFTListing> {
    const listing = await this.findListingById(id);

    // Check ownership
    if (listing.sellerId !== userId) {
      throw new ForbiddenException('You can only update your own listings');
    }

    // Validate status transitions
    if (updateListingDto.status && !this.isValidStatusTransition(listing.status, updateListingDto.status)) {
      throw new BadRequestException(`Cannot change status from ${listing.status} to ${updateListingDto.status}`);
    }

    Object.assign(listing, updateListingDto);
    return await this.listingRepository.save(listing);
  }

  async cancelListing(id: string, userId: string): Promise<NFTListing> {
    const listing = await this.findListingById(id);

    // Check ownership
    if (listing.sellerId !== userId) {
      throw new ForbiddenException('You can only cancel your own listings');
    }

    // Check if listing can be cancelled
    if (listing.status !== ListingStatus.ACTIVE) {
      throw new BadRequestException('Only active listings can be cancelled');
    }

    listing.status = ListingStatus.CANCELLED;
    return await this.listingRepository.save(listing);
  }

  private isValidStatusTransition(currentStatus: ListingStatus, newStatus: ListingStatus): boolean {
    const validTransitions = {
      [ListingStatus.ACTIVE]: [ListingStatus.SOLD, ListingStatus.CANCELLED, ListingStatus.EXPIRED],
      [ListingStatus.SOLD]: [],
      [ListingStatus.CANCELLED]: [],
      [ListingStatus.EXPIRED]: [ListingStatus.ACTIVE], // Can reactivate expired listings
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }
}
