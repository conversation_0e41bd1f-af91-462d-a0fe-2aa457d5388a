# Guidelines Directory Index

## 📋 **Development Guidelines and Standards**

### **🎯 MANDATORY READING FOR ALL DEVELOPERS AND AI AGENTS**

#### **1. Development Rules and Standards** ⭐ **NEW - MANDATORY**
**File:** `development-rules-and-standards.md`
**Purpose:** Comprehensive development rules based on proven best practices
**Scope:** All developers and AI agents
**Status:** Mandatory compliance required

#### **2. AI Agent Guidelines**
**File:** `AI_AGENT_GUIDELINES.md`
**Purpose:** Specific guidelines for AI agents working on the platform
**Scope:** AI agents and automated development tools

#### **3. AI Agent Must Read First**
**File:** `AI_AGENT_MUST_READ_FIRST.md`
**Purpose:** Essential information for AI agents before starting work
**Scope:** AI agents

#### **4. Anti-Duplication Rules**
**File:** `ANTI_DUPLICATION_RULES.md`
**Purpose:** Prevent duplicate services and maintain clean architecture
**Scope:** Service development and architecture decisions

#### **5. Service Template Guidelines**
**File:** `SERVICE_TEMPLATE_GUIDELINES.md`
**Purpose:** Standardized approach for creating new services
**Scope:** Backend service development

#### **6. Working Directory Protocol**
**File:** `WORKING_DIRECTORY_PROTOCOL.md`
**Purpose:** File organization and directory structure standards
**Scope:** All development work

#### **7. API Routing Architecture** ⭐ **NEW - CRITICAL**
**File:** `api-routing-architecture.md`
**Purpose:** Standardized API routing patterns to prevent double prefix issues
**Scope:** All microservices and API development
**Status:** Mandatory compliance - prevents 404 errors and routing failures

## 🚨 **COMPLIANCE REQUIREMENTS**

### **For All Developers:**
- ✅ **MUST READ:** `development-rules-and-standards.md` before starting any work
- ✅ **MUST FOLLOW:** All rules and standards without exception
- ✅ **MUST VERIFY:** Compliance before submitting any code

### **For AI Agents:**
- ✅ **MUST READ:** `AI_AGENT_MUST_READ_FIRST.md` first
- ✅ **MUST READ:** `development-rules-and-standards.md` second
- ✅ **MUST FOLLOW:** All guidelines and protocols
- ✅ **MUST APPLY:** Template-First methodology for all complex operations

## 📚 **Quick Access**

**Most Important Documents:**
1. **Development Rules and Standards** → Core development methodology
2. **API Routing Architecture** → Prevents 404 errors and routing failures
3. **AI Agent Guidelines** → AI-specific requirements
4. **Anti-Duplication Rules** → Architecture consistency

**For New Team Members:**
1. Start with `AI_AGENT_MUST_READ_FIRST.md`
2. Read `development-rules-and-standards.md` thoroughly
3. Review service-specific guidelines as needed

## 🎯 **Purpose**

These guidelines ensure:
- **Consistent Development Practices** across all team members
- **Production-Ready Code** with proper error handling and business rule compliance
- **Comprehensive Documentation** for all issues and solutions
- **Systematic Problem Solving** with root cause identification
- **Maintainable Architecture** following established patterns
