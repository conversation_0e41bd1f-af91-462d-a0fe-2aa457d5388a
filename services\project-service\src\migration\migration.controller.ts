import { Controller, Post, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MigrationService } from './migration.service';

@ApiTags('Database Migration')
@Controller('migration')
export class MigrationController {
  constructor(private readonly migrationService: MigrationService) {}

  @Post('run')
  @ApiOperation({ summary: 'Run database migrations manually' })
  @ApiResponse({ status: 200, description: 'Migration completed successfully' })
  @ApiResponse({ status: 500, description: 'Migration failed' })
  async runMigration() {
    return await this.migrationService.runManualMigration();
  }

  @Get('status')
  @ApiOperation({ summary: 'Check migration status' })
  @ApiResponse({ status: 200, description: 'Migration status retrieved' })
  async getMigrationStatus() {
    // Check if there are any orphaned campaigns
    return {
      status: 'ready',
      message: 'Migration service is ready'
    };
  }
}
