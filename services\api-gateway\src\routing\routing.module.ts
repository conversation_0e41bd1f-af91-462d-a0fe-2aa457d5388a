import { <PERSON>du<PERSON> } from '@nestjs/common';
import { UsersController } from './controllers/users.controller';
import { AuthController } from './controllers/auth.controller';
import { AnalysisController } from './controllers/analysis.controller';
import { ProjectsController } from './controllers/projects.controller';
import { NftGenerationController } from './controllers/nft-generation.controller';
import { BlockchainController } from './controllers/blockchain.controller';
import { EnvironmentController } from './controllers/environment.controller';
import { MarketplaceController } from './controllers/marketplace.controller';
import { ProxyService } from './services/proxy.service';

@Module({
  controllers: [
    UsersController,
    AuthController,
    AnalysisController,
    ProjectsController,
    NftGenerationController,
    BlockchainController,
    EnvironmentController,
    MarketplaceController,
  ],
  providers: [
    ProxyService,
  ],
  exports: [
    ProxyService,
  ],
})
export class RoutingModule {}
